
/* استيراد المتغيرات الموحدة والثيمات */
@import './variables.css';
@import './liquid-theme.css';

html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  direction: rtl;
  font-family: var(--font-family-primary);
}

body {
  background-color: var(--current-bg-secondary);
  color: var(--current-text-primary);
}

/* Animation للتحميل */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


button {
  padding: 12px 25px;
  margin-top: 5px;
  font-size: var(--font-size-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  background-color: var(--primary-color);
  color: rgb(255, 255, 255);
  border: none;
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-light) !important;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

button:active {
  transform: translateY(0);
}

.submit-btn {
  background-color: var(--secondary-color);
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.submit-btn:hover {
  background-color: var(--secondary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.submit-btn:active {
  transform: translateY(0);
}

h2 {
  margin-bottom: 5px;
  font-size: 24px;
  color: #333;
}

p {
  margin-bottom: 5px;
  font-size: 16px;
  color: #555;
}

.case-registration-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--neutral-700);
  margin-top: 16px;
}

.case-registration-container input[type="text"],
.case-registration-container input[type="date"],
.case-registration-container input[type="email"],
.case-registration-container input[type="password"],
.case-registration-container input[type="number"],
.case-registration-container input[type="tel"],
.case-registration-container textarea {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: var(--radius-md);
  font-size: 16px;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid var(--neutral-300);
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.case-registration-container input:focus,
.case-registration-container textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group {
  margin-bottom: 16px;
}

.input-group select,
select {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--radius-md);
  font-family: var(--font-family-primary);
  background-color: white;
  border: 1px solid var(--neutral-300);
  color: var(--neutral-800);
  font-size: var(--font-size-md);
  transition: all var(--transition-fast);
  appearance: none;
  background-repeat: no-repeat;
  background-position: left 16px center;
  padding-left: 40px;
}

.input-group select:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.case-number-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.case-number-inputs input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.separator {
  font-weight: bold;
}

.generated-number {
  margin-top: 5px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

label {
  display: block;
  font-weight: bold;
  margin-top: 5px;
}
/* تم تعريف أنماط الأزرار بالفعل في الأعلى */
/* تنسيقات Progress Bar */
.progress-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: #4CAF50;
  width: 0%;
  transition: width 0.4s ease;
  border-radius: 0 2px 2px 0;
}

/* تعديلات للهيكل العام */
.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 1200px;
  margin: auto;
  padding: 20px;
  gap: 20px;
  box-sizing: border-box;
  position: relative;
  padding-top: 10px; /* إضافة مساحة للـ Progress Bar */
}

