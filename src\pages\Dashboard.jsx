import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaGlobe, FaMobileAlt, FaSearch } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import AssignedTasks from '../components/AssignedTasks';
import styles from './Dashboard.module.css';
import { db } from '../config/firebaseConfig';
import { doc, getDoc } from "firebase/firestore";
import { getActiveAccount, setActiveAccount } from '../services/StorageService';

const Dashboard = ({ currentUser }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({ delayedCasesCount: 0, totalCasesCount: 0 });
  const [loading, setLoading] = useState(true);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const [searchTerm, setSearchTerm] = useState('');

  const CACHE_KEY = `dashboard_stats_${currentUser?.uid || 'guest'}`;
  const CACHE_TTL = 5 * 60 * 1000; // 5 دقائق

  // لم نعد بحاجة إلى وظيفة تبديل الحساب النشط هنا
  // تم نقلها إلى صفحة البروفايل

  useEffect(() => {
    const fetchStats = async (bypassCache = false) => {
      const cachedData = localStorage.getItem(CACHE_KEY);
      if (cachedData && !bypassCache) {
        const { data, timestamp, account } = JSON.parse(cachedData);
        // استخدام التخزين المؤقت فقط إذا كان الحساب النشط هو نفسه المخزن
        if (Date.now() - timestamp < CACHE_TTL && account === activeAccount) {
          setStats(data);
          setLoading(false);
          return;
        }
      }

      setLoading(true);
      try {
        // إذا كان الحساب أونلاين
        if (activeAccount === 'online') {
          // إذا كان الحساب أونلاين وغير متصل بالإنترنت
          if (!navigator.onLine) {
            setStats({ totalCasesCount: 0 });
            setLoading(false);
            return;
          }

          const statsRef = doc(db, 'stats', 'global');
          const statsDoc = await getDoc(statsRef);
          if (statsDoc.exists()) {
            const data = statsDoc.data();
            setStats(data);
            localStorage.setItem(CACHE_KEY, JSON.stringify({
              data,
              timestamp: Date.now(),
              account: activeAccount
            }));
          }
        } else {
          // إذا كان الحساب محلي، استخدم بيانات محلية
          const statsData = {
            totalCasesCount: 0 // يمكن تحسين هذا لاحقًا لحساب الإحصائيات المحلية
          };

          setStats(statsData);

          localStorage.setItem(CACHE_KEY, JSON.stringify({
            data: statsData,
            timestamp: Date.now(),
            account: activeAccount
          }));
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
    const interval = setInterval(() => {
      fetchStats(true);
    }, 30000);

    return () => clearInterval(interval);
  }, [activeAccount]);

  return (
    <div className={styles.dashboard}>
      <TopBar currentUser={currentUser} />
      <main className={styles.content}>
        <div className={styles.header}>
          {/* الشعار */}
          <div className={styles.logoContainer}>
            <img src="/logo.png" alt="شعار التطبيق" className={styles.mainLogo} />
          </div>
          
          {/* خانة البحث */}
          <div className={styles.searchContainer}>
            <div className={styles.searchBox}>
              <FaSearch className={styles.searchIcon} />
              <input
                type="text"
                placeholder="ابحث عن قضية أو موكل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && searchTerm.trim()) {
                    navigate(`/reports?search=${encodeURIComponent(searchTerm.trim())}`);
                  }
                }}
              />
            </div>
          </div>
        </div>
        
        {/* زر قائمة القضايا */}
        <div className={styles.caseListButtonContainer}>
          <button
            className={styles.caseListButton}
            onClick={() => navigate('/reports')}
          >
            قائمة القضايا
          </button>
        </div>

        {/* مكون المهام المكلفة */}
        <div className={styles.assignedTasksSection}>
          <AssignedTasks currentUser={currentUser} />
        </div>
      </main>
    </div>
  );
};

export default Dashboard;