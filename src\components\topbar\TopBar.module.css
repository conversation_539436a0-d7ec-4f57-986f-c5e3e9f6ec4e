/* استخدام متغيرات CSS من الملف الرئيسي */
@import '../../styles/variables.css';

/* حاوي الشريطين */
.headerContainer {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
}

/* شريط التنقل العلوي */
.topBar {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0.3rem 0.8rem 0;
  background: var(--current-bg-primary);
  border-bottom: 1px solid var(--current-border-primary);
  color: var(--current-text-primary);
  transition: all var(--transition-normal);
  backdrop-filter: var(--current-backdrop-blur);
}

.secondaryTopBar {
  display: none; /* إخفاء البار الثاني */
}


/* القسم الأيسر */
.leftSection {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
  min-width: 0;
  padding-top: 0.5rem;
}





/* القسم الأيمن */
.rightSection {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding-top: 0.5rem;
}

/* تنسيق الأزرار الدائرية */
.iconWrapper {
  position: relative;
}

.iconButton {
  background-color: var(--current-bg-secondary);
  width: 45px;
  height: 45px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--current-text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  margin: 0;
  border: 1px solid var(--current-border-primary);
  transition: all 0.3s ease;
  backdrop-filter: var(--current-backdrop-blur);
}

/* تنسيق الأيقونات */
.iconButton svg {
  width: 22px;
  height: 22px;
  stroke-width: 2.2px;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--neutral-600);
  cursor: pointer;
  font-size: 1.8rem;
  font-weight: var(--font-weight-light);
  margin-right: -375px;
}

/* Hide back button when in Dashboard page */
.dashboard .backButton {
  display: none;
}

.backIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.097);
}

.backIconWrapper svg {
  width: 24px;
  height: 24px;
}

.iconButton:hover {
  background-color: var(--current-bg-tertiary);
  color: var(--current-text-primary);
  transform: scale(1.05);
  border-color: var(--primary-color);
  box-shadow: var(--current-shadow-light);
}


.iconButton[aria-label="إضافة قضية جديدة"] {

  color: #444746;
 
  background-size: 200% 200%;

}

.iconButton[aria-label="إضافة قضية جديدة"]:hover {
  background:rgba(70, 70, 70, 0.056);
  color: #444746;
 
}


.iconButton[aria-label="إضافة قضية جديدة"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;

}

.iconButton[aria-label="إضافة قضية جديدة"]:hover::before {
  left: 100%;
}

/* زر الإشعارات بلون أصفر محمر (أصفر أكثر) */
.iconButton[aria-label="الإشعارات"] {
  color:#444746;
}

.iconButton[aria-label="الإشعارات"]:hover {
  background:rgba(70, 70, 70, 0.056);
  color: #444746;
}

/* زر الصفحة الرئيسية */
.iconButton[aria-label="الصفحة الرئيسية"] {
  color: #444746;
}

.iconButton[aria-label="الصفحة الرئيسية"]:hover {
  background: rgba(70, 70, 70, 0.056);
  color: #444746;
}




/* زر المجموعات */
.groupsButton {
  background-color: #000;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all var(--transition-normal);
  padding: 6px;
  margin: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.groupsButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}



.onlineIcon {
  color: var(--info);
}

.localIcon {
  color: var(--success);
}

.tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.iconWrapper:hover .tooltip {
  opacity: 1;
}

/* شارة الإشعارات */
.notificationBadge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid white;
}






/* ملف المستخدم */
.profileWrapper {
  position: relative;
}

.profileButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--current-bg-secondary);
  border: 1px solid var(--current-border-primary);
  color: var(--current-text-primary);
  cursor: pointer;
  padding: 0;
  border-radius: 50%;
  transition: all var(--transition-normal);
  margin: 0;
  width: 35px;
  height: 35px;
  position: relative;
  overflow: hidden;
  aspect-ratio: 1/1;
  backdrop-filter: var(--current-backdrop-blur);
}

.profileButton:hover {
  background-color: var(--current-bg-tertiary);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.profileButton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  pointer-events: none;
}

/* أنماط صورة المستخدم */
.userPhoto {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

/* أنماط الحرف الأول من اسم المستخدم */
.userInitials {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
}

/* تأثيرات الرسوم المتحركة */
@keyframes pulseOnline {
  0% {
    filter: drop-shadow(0 0 3px rgba(52, 152, 219, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 8px rgba(52, 152, 219, 1));
  }
}

@keyframes pulseLocal {
  0% {
    filter: drop-shadow(0 0 3px rgba(46, 204, 113, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 8px rgba(46, 204, 113, 1));
  }
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
}

.userName {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  direction: rtl;
  display: flex;
  align-items: center;
  gap: 6px;
}



/* القائمة المنبثقة */
.profileDropdown {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
  backdrop-filter: blur(4px);
  
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.dropdownItem {
  width: 200px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--current-bg-primary);
  border: 1px solid var(--current-border-primary);
  color: var(--current-text-primary);
  text-align: right;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-weight: 600;
  margin-bottom: 8px;
  box-shadow: var(--current-shadow-medium);
  animation: slideUp 0.4s ease forwards;
  animation-delay: calc(var(--item-index) * 0.1s);
  opacity: 0;
  border-radius: 9999px;
  backdrop-filter: var(--current-backdrop-blur);
}

.dropdownItem:hover {
  background-color: var(--current-bg-secondary);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--current-shadow-heavy);
  border-color: var(--primary-color);
}

.dropdownItem svg {
  color: #4c68c0;
  width: 20px;
  height: 20px;
}

/* Media Query للشاشات الصغيرة (تحسين الهاتف) */
@media (max-width: 768px) {
  .topBar {
    padding: 0.5rem 1rem 0;
    height: 64px;
  }

  .rightSection {
    gap: 0.75rem;
  }

  .iconButton, .groupsButton {
    width: 38px;
    height: 38px;
    padding: 8px;
  }

  .notificationBadge {
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    border-width: 1px;
  }

  .userAvatarOnline,
  .userAvatarLocal {
    width: 45px;
    height: 45px;
  }

  .avatarIconOnline,
  .avatarIconLocal {
    width: 14px;
    height: 14px;
  }
  .secondaryTopBar {
    height: 36px;
    padding: 0 0.75rem;
    border-bottom: none;

    

  }
    .backButton {
  margin-right: -135px;
}

}

  .profileButton {
    padding: 0;
    width: 40px;
    height: 40px;
  }

  .userName {
    font-size: 0.85rem;
    max-width: 80px;
  }

  .statusIndicatorOnline,
  .statusIndicatorLocal {
    width: 6px;
    height: 6px;
  }

  .profileDropdown {
    min-width: 160px;
  }

  .notificationDropdown {
    min-width: 250px;
  }

  .dropdownItem {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }


@media (max-width: 480px) {
  .topBar {
    padding: 0.5rem 0.75rem 0;
    height: 60px;
  }
  
  .secondaryTopBar {
    height: 60px;
    padding: 0 0.75rem;
    border-bottom: 1px solid rgb(218, 220, 224);

  }

  .rightSection {
    gap: 0.5rem;
  }

  .iconButton, .groupsButton {
    width: 36px;
    height: 36px;
    padding: 6px;
  }

  .notificationBadge {
    width: 16px;
    height: 16px;
    font-size: 0.65rem;
  }

  .userAvatarOnline,
  .userAvatarLocal {
    width: 30px;
    height: 30px;
  }

  .avatarIconOnline,
  .avatarIconLocal {
    width: 12px;
    height: 12px;
  }

  .profileButton {
    padding: 0;
    width: 36px;
    height: 36px;
  }

  .userName {
    font-size: 0.8rem;
    max-width: 60px;
  }

  .statusIndicatorOnline,
  .statusIndicatorLocal {
    width: 5px;
    height: 5px;
    margin-right: 2px;
  }

  .profileDropdown {
    min-width: 140px;
  }

  .notificationDropdown {
    min-width: 220px;
  }

  .dropdownItem {
    padding: 0.5rem 0.7rem;
    font-size: 0.8rem;
    gap: 0.4rem;
  }

  .backButton {
  margin-right: -145px;
}

}