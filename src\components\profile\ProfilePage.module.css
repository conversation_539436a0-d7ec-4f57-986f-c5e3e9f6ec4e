@import '../../styles/variables.css';

.pageWrapper {
  min-height: 100vh;
  background: var(--current-bg-secondary);
  display: flex;
  flex-direction: column;
  color: var(--current-text-primary);
  font-family: var(--font-family-primary);
  padding-top: 0;
  transition: all var(--transition-normal);
}

/* الحاوية الرئيسية للمحتوى */
.content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  color: var(--neutral-800);
}

/* العنوان الرئيسي */
.title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--neutral-900);
  margin-bottom: 32px;
  font-size: 2rem;
}

.title .icon {
  color: var(--primary-color);
}

/* تخطيط لوحة التحكم - مطابق لإدارة المجموعات */
.mainContainer {
  display: flex;
  gap: 24px;
  min-height: 70vh;
  align-items: flex-start;
}

/* الشريط الجانبي - مطابق لإدارة المجموعات */
.accountManagementSection {
  width: 280px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: sticky;
  top: 24px;
  align-self: flex-start;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* المحتوى الرئيسي - مطابق لإدارة المجموعات */
.mainContentArea {
  flex: 1;
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  min-height: 600px;
}

/* التبويبات العمودية - مطابق لإدارة المجموعات */
.tabs {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
}

.sidebarNavItem {
  padding: 16px;
  background: none;
  border: none;
  border-right: 3px solid transparent;
  cursor: pointer;
  font-size: 16px;
  color: var(--neutral-600);
  transition: all var(--transition-normal);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: right;
}

.sidebarNavItem:hover {
  background-color: var(--neutral-100);
  color: var(--primary-color);
}

.sidebarNavItem.active {
  color: var(--primary-color);
  background-color: var(--neutral-50);
  border-right-color: var(--primary-color);
  font-weight: 600;
}

.sidebarNavIcon {
  font-size: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* بطاقات الإحصائيات - مطابق لإدارة المجموعات */
.statsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.statCard {
  background-color: var(--current-bg-primary);
  border-radius: var(--radius-lg);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--current-shadow-medium);
  border: 1px solid var(--current-border-primary);
  transition: all var(--transition-normal);
  backdrop-filter: var(--current-backdrop-blur);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--current-shadow-heavy);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.statInfo {
  display: flex;
  flex-direction: column;
}

.statValue {
  font-size: 24px;
  font-weight: 700;
  color: var(--current-text-primary);
}

.statLabel {
  font-size: 14px;
  color: var(--current-text-secondary);
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--current-border-primary);
}

.pageTitle {
  color: var(--current-text-primary);
  font-size: 1.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerIcon {
  color: var(--primary-color);
  font-size: 24px;
}

/* Personal Info Section */
.personalInfoSection {
  margin-bottom: 32px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
}

.sectionHeader h2 {
  color: var(--neutral-900);
  font-size: 1.5rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.profileDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow: hidden;
}

.profileField {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 16px;
  align-items: center;
  padding: 16px 0;
  min-height: 50px;
  border-bottom: 1px solid #dadce0;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.profileField:hover {
  background-color: #f8f9fa;
}

.profileField label {
  color: #5f6368;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  white-space: nowrap;
}

.fieldIcon {
  color: #5f6368;
  font-size: 1.2rem;
}

.profileField span,
.profileField input {
  color: #202124;
  text-align: right;
  word-break: break-word;
  font-size: 1.1rem;
  background: transparent;
  padding: 8px 0;
  border-radius: 4px;
  border: none;
  width: 100%;
}

.profileField input {
  background: #ffffff;
  border: 1px solid #dadce0;
  padding: 8px 12px;
}

.profileField input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3);
}

/* Form Group for Edit Field */
.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.formGroup label {
  color: #5f6368;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.formInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 16px;
  color: #202124;
  background: #ffffff;
}

.formInput:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3);
}

/* أنماط صفحة تغيير الصورة */
.photoUploadContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
}

.photoPreviewArea {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #dadce0;
  margin-bottom: 20px;
}

.photoPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photoPlaceholder {
  width: 100%;
  height: 100%;
  background-color: #f1f3f4;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #5f6368;
}

.photoActions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.selectPhotoButton {
  background-color: #1a73e8;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.selectPhotoButton:hover {
  background-color: #1765cc;
}

/* Buttons */
.buttonRow {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 0 0 0;
  flex-shrink: 0;
  border-top: 1px solid #dadce0;
  margin-top: 16px;
}

.saveButton,
.cancelButton {
  padding: 0 var(--spacing-xl);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  height: 36px;
  text-transform: uppercase;
  letter-spacing: 0.25px;
}

.saveButton {
  background: #1a73e8;
  color: #fff;
}
.saveButton:hover { 
  background: #1765cc; 
  box-shadow: 0 1px 2px 0 rgba(26,115,232,0.3); 
}

.cancelButton {
  background: #ffffff;
  color: #5f6368;
  border: 1px solid #dadce0;
}
.cancelButton:hover { 
  background: #f1f3f4; 
}

.deleteAccountButton {
  background: transparent;
  color: #dc3545;
  border: 1px solid #dc3545;
  border-radius: 12px;
  padding: 10px 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.deleteAccountButton:hover {
  background: #dc3545;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.deleteAccountButton:active {
  transform: translateY(0);
  box-shadow: none;
  background: #c82333;
}

/* Section Titles */
.sectionTitle {
  color: #202124;
  margin: 0 0 20px 0;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #dadce0;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-left: 4px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}
@keyframes spin { 
  0% { transform: rotate(0deg); } 
  100% { transform: rotate(360deg); } 
}

.errorContainer { 
  color: #ff8a80; 
  background: rgba(255, 138, 128, 0.2); 
  border: 1px solid rgba(255, 138, 128, 0.4); 
}

.errorMessage { 
  color: #ff8a80; 
  background: rgba(255, 138, 128, 0.1); 
  padding: 10px; 
  border-radius: 6px; 
  margin-bottom: 10px; 
  border: 1px solid rgba(255, 138, 128, 0.3); 
  text-align: center; 
  font-size: 0.9rem; 
}

/* Modal Styles */
.modalOverlay { 
  position: fixed; 
  top: 0; 
  left: 0; 
  right: 0; 
  bottom: 0; 
  background: rgba(0, 0, 0, 0.5); 
  display: flex; 
  justify-content: center; 
  align-items: center; 
  z-index: 10000; 
  animation: fadeIn 0.3s ease-out; 
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  direction: rtl;
  animation: slideIn 0.3s ease-out;
}

.modal h3 {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.modalButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
}

.confirmButton,
.deleteButton {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.confirmButton {
  background: #1976d2;
  color: white;
}

.confirmButton:hover {
  background: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.deleteButton {
  background: #d32f2f;
  color: white;
}

.deleteButton:hover {
  background: #c62828;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.profileInfoWithImage {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 18px;
  font-size: 1.07rem;
  color: #444;
  line-height: 1.9;
  margin: 0 0 18px 0;
  direction: rtl;
  text-align: right;
}

.profileHeaderContainer {
  gap: 20px;
}

.profileHeaderImage {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.accountCardsDivider {
  width: 100%;
  height: 1px;
  background: #e0e7ef;
  margin: 24px 0 0 0;
  border: none;
}

.accountCardsContainer {
  display: flex;
  gap: 24px;
  margin: 24px 0;
  padding: 20px;
  background: linear-gradient(145deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.editFieldForm {
  background: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
  padding: 30px;
  max-width: 800px;
  width: 100%;
  margin-left: 10px;
  margin-right: 40px;
}

/* مؤشر التحميل - مطابق لإدارة المجموعات */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--neutral-200);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: var(--animation-spin);
  margin-bottom: 16px;
}

/* تصميم متجاوب - مطابق لإدارة المجموعات */
@media (max-width: 1024px) {
  .mainContainer {
    flex-direction: column;
  }

  .accountManagementSection {
    width: 100%;
    position: static;
  }

  .tabs {
    flex-direction: row;
    overflow-x: auto;
  }

  .sidebarNavItem {
    border-right: none;
    border-bottom: 3px solid transparent;
  }

  .sidebarNavItem.active {
    border-right-color: transparent;
    border-bottom-color: var(--primary-color);
  }

  .statsContainer {
    flex-direction: row;
  }

  .statCard {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .mainContainer {
    flex-direction: column;
    height: auto;
    padding: 10px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 110px;
  }

  .accountManagementSection {
    width: 100%;
    height: 40px;
    position: fixed;
    top: 96px;
    right: 0;
    left: 0;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row;
    overflow-x: visible;
    background-color: #ffffff;
    box-shadow: none;
    z-index: 99;
    border-bottom: 1px solid #f1f3f4;
  }

  .sidebarNavItem {
    padding: 0 5px;
    border: none;
    flex: 1;
    min-width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    height: 40px;
    text-align: center;
    line-height: 1.2;
    background-color: #ffffff;
  }

  .sidebarNavIcon {
    display: none;
  }

  .sidebarNavItem.active {
    border-left: none;
    border-bottom: 3px solid #1a73e8;
    color: #1a73e8;
    font-weight: 600;
  }

  .mainContentArea {
    min-height: auto;
    padding: 15px;
  }

  .pageHeader {
    padding: 10px 15px;
    width: 100%;
    text-align: center;
  }

  .pageTitle {
    font-size: 1.4rem;
  }

  .personalInfoSection {
    margin-right: 0;
    margin-left: 0;
    width: 100%;
    padding: 20px 15px;
  }

  .profileField {
    grid-template-columns: 1fr;
    gap: 5px;
    padding: 10px;
  }

  .profileField label {
    justify-content: flex-start;
    font-size: 0.8rem;
  }

  .profileField span,
  .profileField input {
    text-align: left;
    font-size: 0.85rem;
  }

  .buttonRow {
    justify-content: center;
  }

  .profileInfoWithImage {
    flex-direction: column-reverse;
    align-items: stretch;
    text-align: right;
  }

  .accountCardsContainer {
    flex-direction: column;
    gap: 16px;
    margin: 16px 0;
  }
  
  .accountCardsContainer > div {
    min-width: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .modal {
    width: 95%;
    padding: 20px;
    margin: 10px;
  }
  
  .modalButtons {
    flex-direction: column;
    gap: 12px;
  }
  
  .confirmButton,
  .cancelButton,
  .deleteButton {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
  }
  
  .modal h3 {
    font-size: 1.2rem;
  }
  
  .modal ul {
    font-size: 0.9rem;
    padding-right: 15px;
  }
}

@media (max-width: 400px) {
  .profileHeaderContainer {
    flex-direction: column;
    gap: 24px;
    margin-left: 0;
    text-align: center;
    margin-right: 10px;
  }
}

