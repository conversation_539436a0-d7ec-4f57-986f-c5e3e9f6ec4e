import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiUser, FiFileText } from 'react-icons/fi';
import styles from './AssignedTasks.module.css';
import { getUserTaskAssignments, getUserTaskAssignmentsDebug, getGroupTaskAssignments, fixExistingAssignments } from '../services/GroupsService';
import { getActiveAccount } from '../services/StorageService';

const AssignedTasks = ({ currentUser }) => {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeAccount] = useState(getActiveAccount());

  useEffect(() => {
    const fetchAssignments = async () => {
      if (!currentUser || activeAccount !== 'online') {
        setLoading(false);
        return;
      }

      try {
        console.log('جلب المهام للمستخدم:', currentUser.uid);
        
        // أولاً، إصلاح التكليفات الموجودة
        await fixExistingAssignments(currentUser.uid);
        
        // استخدام نفس الطريقة المستخدمة في GroupsManagement
        const groupAssignments = await getGroupTaskAssignments(currentUser.uid);
        console.log('جميع تكليفات المجموعة:', groupAssignments);
        
        // فلترة التكليفات للمستخدم الحالي فقط
        const userAssignments = groupAssignments.filter(assignment => {
          // التحقق من التطابق بطرق متعددة للتوافق مع التكليفات القديمة والجديدة
          const assignedToId = assignment.assignedTo?.id;
          const assignedToUserId = assignment.assignedTo?.userId;
          
          const isAssignedToCurrentUser = 
            assignedToId === currentUser.uid || 
            assignedToUserId === currentUser.uid;
            
          console.log('فحص التكليف بالتفصيل:', {
            assignmentId: assignment.id,
            assignedTo: assignment.assignedTo,
            assignedToId: assignedToId,
            assignedToUserId: assignedToUserId,
            currentUserId: currentUser.uid,
            isMatch: isAssignedToCurrentUser,
            matchById: assignedToId === currentUser.uid,
            matchByUserId: assignedToUserId === currentUser.uid
          });
          return isAssignedToCurrentUser;
        });
        
        console.log('التكليفات المفلترة للمستخدم:', userAssignments);
        setAssignments(userAssignments);
      } catch (error) {
        console.error('خطأ في جلب المهام المكلفة:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [currentUser, activeAccount]);

  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTaskIcon = (taskType) => {
    return taskType === 'deferral' ? <FiCalendar /> : <FiClock />;
  };

  const getTaskDate = (assignment) => {
    if (assignment.taskType === 'deferral') {
      return assignment.taskData?.date;
    } else {
      return assignment.taskData?.deadline;
    }
  };

  if (activeAccount !== 'online') {
    return (
      <div className={styles.offlineMessage}>
        <FiUser className={styles.offlineIcon} />
        <p>المهام المكلفة متاحة فقط في الوضع الأونلاين</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>جاري تحميل المهام المكلفة...</p>
      </div>
    );
  }

  return (
    <div className={styles.assignedTasksContainer}>
      <div className={styles.header}>
        <h3>
          <FiFileText className={styles.headerIcon} />
          المهام المكلف بها
        </h3>
        <span className={styles.taskCount}>
          {assignments.length} مهمة
        </span>
      </div>

      {assignments.length > 0 ? (
        <div className={styles.tasksList}>
          {assignments.map((assignment, index) => (
            <div key={assignment.id || index} className={styles.taskCard}>
              <div className={styles.taskHeader}>
                <div className={styles.taskIcon}>
                  {getTaskIcon(assignment.taskType)}
                </div>
                <div className={styles.taskInfo}>
                  <h4 className={styles.taskTitle}>
                    {assignment.taskType === 'deferral' ? (
                      assignment.taskData?.reasons?.join('، ') || 'تأجيل'
                    ) : (
                      assignment.taskData?.description || 'إجراء'
                    )}
                  </h4>
                  <p className={styles.taskMeta}>
                    كُلفت بواسطة: {assignment.assignedBy?.name || 'غير محدد'}
                  </p>
                </div>
              </div>
              
              <div className={styles.taskDetails}>
                <div className={styles.taskDateAndBadge}>
                  <div className={styles.taskDate}>
                    <FiCalendar className={styles.dateIcon} />
                    <span>
                      {formatDate(getTaskDate(assignment))}
                    </span>
                  </div>
                  <div className={styles.taskBadge}>
                    <span className={`${styles.typeBadge} ${
                      assignment.taskType === 'deferral' ? styles.deferralBadge : styles.actionBadge
                    }`}>
                      {assignment.taskType === 'deferral' ? 'تأجيل' : 'إجراء'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={styles.noTasks}>
          <FiFileText className={styles.noTasksIcon} />
          <p>لا توجد مهام مكلف بها حالياً</p>
        </div>
      )}
    </div>
  );
};

export default AssignedTasks;