# إعداد نظام توزيع المهام

## المشكلة الحالية
تظهر رسالة خطأ: `Missing or insufficient permissions` عند محاولة تكليف المهام.

## الحل: تحديث قواعد Firebase Firestore

### الخطوات:

1. **انتقل إلى Firebase Console**
   - افتح [Firebase Console](https://console.firebase.google.com)
   - اختر مشروعك

2. **انتقل إلى Firestore Database**
   - من القائمة الجانبية، اختر "Firestore Database"
   - اضغط على تبويب "Rules"

3. **تحديث القواعد**
   - استبدل القواعد الحالية بالقواعد التالية:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد المجموعات الموجودة
    match /groups/{groupId} {
      allow read, write: if request.auth != null;
    }
    
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }
    
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /cases/{caseId} {
      allow read, write: if request.auth != null;
    }
    
    match /stats/{document} {
      allow read, write: if request.auth != null;
    }
    
    match /sharedCases/{sharedCaseId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد جديدة لنظام توزيع المهام
    match /taskAssignments/{assignmentId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null 
        && request.auth.uid != null
        && resource == null;
      allow update: if request.auth != null 
        && request.auth.uid != null;
      allow delete: if request.auth != null 
        && request.auth.uid != null;
    }
  }
}
```

4. **نشر القواعد**
   - اضغط على زر "Publish" لحفظ التغييرات
   - انتظر حتى يتم تطبيق القواعد (قد يستغرق بضع دقائق)

## التحقق من نجاح الإعداد

بعد تحديث القواعد، جرب:

1. **تسجيل الدخول** إلى التطبيق
2. **انتقل إلى صفحة تفاصيل قضية**
3. **اضغط على زر "تكليف"** بجانب أي مهمة
4. **اختر عضو** من القائمة المنسدلة
5. **تأكد من ظهور رسالة نجاح**

## الميزات المتاحة بعد الإعداد

### 1. تكليف المهام
- زر تكليف بجانب كل مهمة (تأجيل أو إجراء)
- قائمة منسدلة لاختيار العضو
- عرض العضو المكلف حالياً

### 2. عرض المهام المكلفة
- **في الصفحة الرئيسية**: جميع المهام المكلف بها المستخدم
- **في إدارة المجموعات**: أول مهمة لكل عضو في جدول التكليفات

### 3. إدارة التكليفات
- إلغاء التكليف
- تحديث التكليف
- تتبع تاريخ التكليف

## استكشاف الأخطاء

### إذا استمر ظهور خطأ الصلاحيات:
1. تأكد من تطبيق القواعد الجديدة
2. تأكد من تسجيل الدخول بحساب صحيح
3. تحقق من وجود اتصال بالإنترنت
4. جرب تحديث الصفحة

### إذا لم تظهر المهام:
1. تأكد من وجود مجموعات وأعضاء
2. تأكد من تكليف مهام فعلاً
3. تحقق من وضع الحساب (يجب أن يكون أونلاين)

## ملاحظات مهمة

- نظام التكليف يعمل فقط في **الوضع الأونلاين**
- فقط **المدراء والمحررين** يمكنهم تكليف المهام
- يتم حفظ التكليفات في مجموعة `taskAssignments` في Firestore
- التكليفات مرتبطة بالمهام والقضايا والمجموعات

## الدعم

إذا واجهت أي مشاكل، تحقق من:
- Console المتصفح للأخطاء
- Firebase Console للتأكد من القواعد
- حالة الاتصال بالإنترنت
- صلاحيات المستخدم في المجموعة