import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaSave, FaTimes, FaBuilding, FaUsers, FaGlobe, FaMobileAlt, FaUser, FaEnvelope, FaPhone, FaUserTie, FaLock, FaChevronLeft } from 'react-icons/fa';
import { FiSettings } from 'react-icons/fi';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';
import { db } from '../../config/firebaseConfig';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, writeBatch } from 'firebase/firestore';
import { getActiveAccount, setActiveAccount } from '../../services/StorageService';
import EditFieldForm from './EditFieldForm'; //  تعديل لاستيراد التصدير الافتراضي
import ProfileFields from './ProfileFields'; //  تعديل لاستيراد التصدير الافتراضي
import { ThemeSelector } from '../ui';
const ProfilePage = ({ currentUser }) => {
  const [activeSection, setActiveSection] = useState('personal'); // personal, people, payments, security
  const navigate = useNavigate();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    jobTitle: '',
    photoURL: ''
  });
  const [editingField, setEditingField] = useState(null);

  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const [localUserData, setLocalUserData] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteAccountType, setDeleteAccountType] = useState('');
  const [passwordConfirm, setPasswordConfirm] = useState('');

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser || !currentUser.uid) {
        setError('المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      // تحميل بيانات الحساب الأونلاين
      try {
        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const data = userSnap.data();
          setUserData(data);
          if (activeAccount === 'online') {
            setFormData({
              name: data.name || data.username || '',
              email: data.email || '',
              phone: data.phone || '',
              company: data.company || '',
              jobTitle: data.jobTitle || '',
              photoURL: data.photoURL || '',
            });
          }

        } else {
          console.log('لم يتم العثور على بيانات المستخدم في Firestore.');
        }
      } catch (e) {
        console.error('خطأ في جلب بيانات المستخدم من Firestore:', e.message);
      }

      // تحميل بيانات الحساب المحلي
      try {
        const localData = localStorage.getItem('localUserData_' + currentUser.uid);
        if (localData) {
          const parsedLocalData = JSON.parse(localData);
          setLocalUserData(parsedLocalData);
          if (activeAccount === 'local') {
            setFormData({
              name: parsedLocalData.name || parsedLocalData.username || '',
              email: parsedLocalData.email || '',
              phone: parsedLocalData.phone || '',
              company: parsedLocalData.company || '',
              jobTitle: parsedLocalData.jobTitle || '',
              photoURL: parsedLocalData.photoURL || '',
            });
          }
        } else {
          console.log('لم يتم العثور على بيانات محلية للمستخدم.');
        }
      } catch (e) {
        console.error('خطأ في قراءة البيانات المحلية:', e.message);
      }

      setLoading(false);
    };

    fetchUserData();

    const savedActiveAccount = localStorage.getItem('activeAccount');
    if (savedActiveAccount) {
      setActiveAccount(savedActiveAccount);
    }

    const handleOnlineStatusChange = () => {
      if (!navigator.onLine && activeAccount === 'online') {
        alert('أنت الآن غير متصل بالإنترنت. قد تواجه مشكلات في الوصول إلى بيانات الحساب الأونلاين. يمكنك التبديل إلى الحساب المحلي للاستمرار في العمل دون اتصال.');
      }
    };
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, [currentUser, activeAccount]);





  const handleSave = async () => {
    if (!currentUser?.uid) {
      setError('المستخدم غير مسجل الدخول.');
      return;
    }
    
    // Only validate the field being edited
    if (editingField === 'name' && !formData.name) {
      setError('الاسم مطلوب.');
      return;
    }

    try {
      setError(null);

      // Create an update object with only the field being edited
      const updateData = {};
      if (editingField) {
        updateData[editingField] = formData[editingField];
      }

      if (activeAccount === 'online') {
        if (navigator.onLine) {
          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, {
            ...updateData,
            lastUpdateTime: new Date().toISOString()
          });
          setUserData({ ...userData, ...updateData, lastUpdateTime: new Date().toISOString() });
          alert('تم حفظ البيانات بنجاح في الحساب الأونلاين.');
        } else {
          setError('لا يمكن حفظ البيانات في الحساب الأونلاين بدون اتصال بالإنترنت.');
        }
      } else { // activeAccount === 'local'
        const updatedLocalData = {
          ...(localUserData || {}),
          uid: currentUser.uid,
          ...updateData,
          lastUpdatedAt: new Date().toISOString()
        };
        localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(updatedLocalData));
        setLocalUserData(updatedLocalData);
        alert('تم حفظ البيانات بنجاح في الحساب المحلي.');
      }
      
      // Return to personal section after saving
      setActiveSection('personal');
      setEditingField(null);
      
    } catch (e) {
      setError('خطأ في تحديث البيانات: ' + e.message);
      if (activeAccount === 'online' && !navigator.onLine) {
        if (window.confirm('حدث خطأ في حفظ البيانات في الحساب الأونلاين. هل ترغب في التبديل إلى الحساب المحلي؟')) {
          switchToLocalAccount();
        }
      }
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };





  const switchToLocalAccount = () => {
    setActiveAccount('local');
    setActiveAccountState('local');
    window.location.reload();
  };

  const createLocalAccount = () => {
    if (!currentUser?.uid) return;
    
    // إضافة loading state
    const originalButton = document.querySelector('button[style*="background: rgb(56, 142, 60)"]');
    if (originalButton) {
      originalButton.innerHTML = '⏳ جاري الإنشاء...';
      originalButton.disabled = true;
    }
    
    setTimeout(() => {
      const newLocalUserData = {
        uid: currentUser.uid,
        name: '',
        email: currentUser.email || '',
        phone: '',
        company: '',
        jobTitle: '',
        photoURL: '',
        cases: [],
        createdAt: new Date().toISOString(),
        lastUpdatedAt: new Date().toISOString()
      };
      localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(newLocalUserData));
      setLocalUserData(newLocalUserData);
      setActiveAccount('local');
      setActiveAccountState('local');
      setShowConfirmModal(false);
      
      // رسالة نجاح محسنة
      alert('🎉 تهانينا!\n\nتم إنشاء حساب أوفلاين جديد بنجاح والتبديل إليه.\n\n• يمكنك الآن العمل بدون اتصال بالإنترنت\n• جميع البيانات ستُحفظ على جهازك\n• يمكنك التبديل بين الحسابات في أي وقت');
      window.location.reload();
    }, 1000); // تأخير لإظهار حالة التحميل
  };

  const cancelCreateLocalAccount = () => setShowConfirmModal(false);

  const handleDeleteAccountClick = (accountType) => {
    setDeleteAccountType(accountType);
    setPasswordConfirm('');
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!passwordConfirm) {
      alert('يرجى إدخال كلمة المرور للتأكيد');
      return;
    }

    if (passwordConfirm.length < 6) {
      alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    if (deleteAccountType === 'online') {
      try {
        // حذف جميع القضايا من Firestore
        const casesRef = collection(db, 'cases');
        const q = query(casesRef, where('userId', '==', currentUser.uid));
        const querySnapshot = await getDocs(q);
        
        const batch = writeBatch(db);
        
        // حذف القضايا الرئيسية
        querySnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        // حذف القضايا المرتبطة
        const linkedCasesQuery = query(casesRef, where('linkedCaseId', '==', currentUser.uid));
        const linkedCasesSnapshot = await getDocs(linkedCasesQuery);
        linkedCasesSnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        // حذف القضايا المشتركة
        const sharedCasesQuery = query(casesRef, where('sharedWith', 'array-contains', currentUser.uid));
        const sharedCasesSnapshot = await getDocs(sharedCasesQuery);
        sharedCasesSnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        await batch.commit();

        // حذف بيانات المستخدم
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, {
          isDeleted: true,
          deletedAt: new Date().toISOString()
        });

        setUserData(null);
        setShowDeleteModal(false);
        setPasswordConfirm('');
        alert('تم حذف الحساب الأونلاين وجميع القضايا المرتبطة به بنجاح.');
      } catch (error) {
        console.error('خطأ في حذف البيانات:', error);
        alert('حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.');
      }
    } else if (deleteAccountType === 'local') {
      try {
        // حذف جميع القضايا المحلية وبياناتها التفصيلية
        const localCases = JSON.parse(localStorage.getItem('cases_' + currentUser.uid) || '[]');
        localCases.forEach(caseItem => {
          // حذف بيانات كل قضية بشكل منفصل
          localStorage.removeItem(`localCase_${currentUser.uid}_${caseItem.id}`);
          // حذف أي إشعارات مرتبطة بالقضية
          localStorage.removeItem(`localNotifications_${currentUser.uid}_${caseItem.id}`);
        });

        // حذف قائمة القضايا الرئيسية
        localStorage.removeItem('cases_' + currentUser.uid);
        
        // حذف بيانات المستخدم المحلية
        localStorage.removeItem('localUserData_' + currentUser.uid);
        
        // حذف الإشعارات العامة
        localStorage.removeItem('localNotifications_' + currentUser.uid);

        // حذف أي بيانات إضافية مرتبطة بالمستخدم
        const allKeys = Object.keys(localStorage);
        const userKeys = allKeys.filter(key => key.includes(currentUser.uid));
        userKeys.forEach(key => localStorage.removeItem(key));
        
        setLocalUserData(null);
        if (activeAccount === 'local') {
          setActiveAccount('online');
          setActiveAccountState('online');
        }
        setShowDeleteModal(false);
        setPasswordConfirm('');
        alert('تم حذف الحساب المحلي وجميع البيانات المرتبطة به بنجاح.');
      } catch (error) {
        console.error('خطأ في حذف البيانات المحلية:', error);
        alert('حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  const cancelDeleteAccount = () => {
    setShowDeleteModal(false);
    setPasswordConfirm('');
    setDeleteAccountType('');
  };



  if (loading) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <div className={styles.loadingContainer}><div className={styles.spinner}></div><p>جاري التحميل...</p></div>
      </div>
    );
  }

  if (error && !(activeAccount === 'online' ? userData : localUserData)) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <div className={styles.errorContainer}>{error || 'خطأ في تحميل البيانات.'}</div>
      </div>
    );
  }

  // Determine current data based on active account
  const currentDisplayData = activeAccount === 'online' ? userData : localUserData;

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}> 
        {/* الشريط الجانبي - مطابق لإدارة المجموعات */}
        <div className={styles.accountManagementSection}>
          <div className={styles.tabs}>
            <div 
              className={`${styles.sidebarNavItem} ${activeSection === 'personal' ? styles.active : ''}`}
              onClick={() => setActiveSection('personal')}
            >
              <FaUser className={styles.sidebarNavIcon} />
              <span>المعلومات الشخصية</span>
            </div>
            <div 
              className={`${styles.sidebarNavItem} ${activeSection === 'people' ? styles.active : ''}`}
              onClick={() => setActiveSection('people')}
            >
              <FaUsers className={styles.sidebarNavIcon} />
              <span>البيانات والتخزين</span>
            </div>
            <div
              className={`${styles.sidebarNavItem} ${activeSection === 'security' ? styles.active : ''}`}
              onClick={() => setActiveSection('security')}
            >
              <FaLock className={styles.sidebarNavIcon} />
              <span>الأمان</span>
            </div>
            <div
              className={`${styles.sidebarNavItem} ${activeSection === 'themes' ? styles.active : ''}`}
              onClick={() => setActiveSection('themes')}
            >
              <FiSettings className={styles.sidebarNavIcon} />
              <span>المظهر والثيمات</span>
            </div>
            <div 
              className={`${styles.sidebarNavItem} ${activeSection === 'payments' ? styles.active : ''}`}
              onClick={() => setActiveSection('payments')}
            >
              <FaBuilding className={styles.sidebarNavIcon} />
              <span>الدفعات والاشتراكات</span>
            </div>
          </div>

          <div className={styles.statsContainer}>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>
                <FaUser />
              </div>
              <div className={styles.statInfo}>
                <span className={styles.statValue}>
                  {activeAccount === 'online' ? (userData ? '1' : '0') : (localUserData ? '1' : '0')}
                </span>
                <span className={styles.statLabel}>الحساب النشط</span>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={styles.statIcon}>
                <FaGlobe />
              </div>
              <div className={styles.statInfo}>
                <span className={styles.statValue}>
                  {activeAccount === 'online' ? 'أونلاين' : 'محلي'}
                </span>
                <span className={styles.statLabel}>نوع الحساب</span>
              </div>
            </div>
          </div>
        </div>

        {/* المحتوى الرئيسي - مطابق لإدارة المجموعات */} 
        <div className={styles.mainContentArea}> 
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>
              {activeSection === 'personal' && (
                <>
                  <FaUser className={styles.headerIcon} />
                  المعلومات الشخصية
                </>
              )}
              {activeSection === 'people' && (
                <>
                  <FaUsers className={styles.headerIcon} />
                  البيانات والتخزين
                </>
              )}
              {activeSection === 'payments' && (
                <>
                  <FaBuilding className={styles.headerIcon} />
                  الدفعات والاشتراكات
                </>
              )}
              {activeSection === 'security' && (
                <>
                  <FaLock className={styles.headerIcon} />
                  الأمان
                </>
              )}
              {activeSection === 'themes' && (
                <>
                  <FiSettings className={styles.headerIcon} />
                  المظهر والثيمات
                </>
              )}
            </h2>
          </div>

          {error && <div className={styles.errorMessage}>{error}</div>}

          {/* Personal Info Section */}
          {activeSection === 'personal' && (
            <div className={styles.personalInfoSection}>
              <div className={styles.profileHeaderContainer} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '150px',
                marginBottom: '20px',
                marginRight: '16px'
              }}>
                <div>
                  <h1 style={{ 
                    fontSize: '1.5rem', 
                    fontWeight: '600', 
                    marginBottom: '8px',
                    color: '#202124'
                  }}>معلومات ملفكَ الشخصي في خدمات تطبيق الاجندة القضائية.</h1>
                  <p style={{ 
                    fontSize: '0.95rem', 
                    color: '#5f6368', 
                    margin: 0
                    
                  }}>تشمل المعلومات الشخصية والخيارات المتاحة لإدارتها. ويمكنكَ إظهار بعض هذه المعلومات.</p>
                </div>
                <img 
                  src="/photo.png" 
                  alt="صورة توضيحية" 
                  className={styles.profileHeaderImage}
                  style={{
                    width: '180px',
                    height: '180px'
                  }}
                />
              </div>
              
              <ProfileFields 
                currentDisplayData={currentDisplayData} 
              />
            </div>
          )}

          {/* People and Sharing Section */}
          {activeSection === 'people' && (
            <div className={styles.personalInfoSection}> 
          {/* وصف مختصر وصورة تحت العنوان - تم نقلها خارج الحاوية الرئيسية */}
          
          {/* التعليق التوضيحي فوق الكروت */}
          <div className={styles.profileInfoWithImage} style={{display: 'flex', alignItems: 'flex-start', gap: '18px', fontSize: '1.07rem', color: '#444', lineHeight: 1.9, margin: '0 0 18px 0', direction: 'rtl', textAlign: 'right'}}>
          <div style={{flex: 1}}>
          <div style={{marginBottom: '8px'}}>
          نوفر لكم إمكانية إنشاء <strong>حساب واحد فقط من كل نوع</strong>:
          </div>
          <ul style={{margin: '0 0 10px 24px', padding: 0, fontSize: '1rem', listStylePosition: 'inside', direction: 'rtl', textAlign: 'right'}}>
          <li style={{marginBottom: '4px'}}>حساب <strong>سحابي (أونلاين)</strong> يُخزّن البيانات على قاعدة بيانات ��حابية للوصول إليها من أي مكان.</li>
          <li>حساب <strong>محلي (أوفلاين)</strong> يُخزّن البيانات على الجهاز للاستخدام بدون اتصال بالإنترنت.</li>
          </ul>
          <div style={{marginBottom: '6px'}}>يمكنكم أيضًا <strong>حذف أي حساب</strong> في أي وقت لضمان التحكم الكامل في بياناتكم.</div>
                    </div>
          <img src="/photo2.png" alt="توضيح" className={styles.centerOnMobile} style={{width: '200px', height: '200px', borderRadius: '12px', objectFit: 'cover', background: '#f5f5f5', flexShrink: 0, marginTop: '20px'}} />
          </div>
          
          <div className={styles.profileDetails}>
          {/* ...باقي الخانات هنا... */}
          
          
          {/* كروت الحسابات */}
          <div className={styles.accountCardsContainer}>
                {/* كارت الحساب الأونلاين */}
                <div style={{
                  flex: '1',
                  border: activeAccount === 'online' ? '2px solid #1976d2' : '1px solid #e0e7ef',
                  borderRadius: '20px',
                  background: activeAccount === 'online' ? 'linear-gradient(145deg, #f0f7ff 0%, #ffffff 100%)' : 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                  boxShadow: activeAccount === 'online' ? '0 8px 24px rgba(25, 118, 210, 0.12)' : '0 4px 16px rgba(0, 0, 0, 0.05)',
                  padding: '32px',
                  minWidth: '280px',
                  textAlign: 'center',
                  position: 'relative',
                  cursor: 'pointer',
                  opacity: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  transition: 'all 0.3s ease',
                  transform: activeAccount === 'online' ? 'translateY(-4px)' : 'none',
                }}
                onClick={() => {
                  setActiveAccount('online');
                  setActiveAccountState('online');
                }}
                >
                  {activeAccount === 'online' && (
                    <div style={{
                      marginBottom: 16,
                      background: 'linear-gradient(135deg, #1976d2 0%, #2196f3 100%)',
                      color: '#fff',
                      borderRadius: '12px',
                      padding: '6px 16px',
                      fontSize: '0.9rem',
                      fontWeight: '600',
                      alignSelf: 'center',
                      boxShadow: '0 4px 12px rgba(25, 118, 210, 0.15)',
                      letterSpacing: '0.3px',
                    }}>
                      الحساب الحالي
                    </div>
                  )}
                  <div style={{
                    background: activeAccount === 'online' ? 'rgba(25, 118, 210, 0.1)' : 'rgba(0, 0, 0, 0.03)',
                    borderRadius: '50%',
                    padding: '20px',
                    marginBottom: '16px',
                    transition: 'all 0.3s ease'
                  }}>
                    <FaGlobe size={40} color={activeAccount === 'online' ? '#1976d2' : '#666'} />
                  </div>
                  <div style={{
                    fontWeight: '600',
                    fontSize: '1.2rem',
                    marginBottom: '8px',
                    color: activeAccount === 'online' ? '#1976d2' : '#333'
                  }}>الحساب الأونلاين</div>
                  <div style={{
                    fontSize: '1rem',
                    color: '#444',
                    marginBottom: '12px',
                    maxWidth: '90%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>{userData?.email || currentUser?.email || ''}</div>
                  <div style={{
                    fontSize: '0.9rem',
                    color: '#666',
                    marginBottom: '16px',
                    background: 'rgba(0, 0, 0, 0.03)',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    width: '90%'
                  }}>بياناتك محفوظة سحابياً</div>
                  <div style={{flex: 1}}></div>
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      handleDeleteAccountClick('online');
                    }}
                    className={styles.deleteAccountButton}
                    style={{
                      marginTop: 18,
                      width: '90%',
                      background: 'rgba(220, 53, 69, 0.05)',
                      backdropFilter: 'blur(8px)',
                      border: '1px solid rgba(220, 53, 69, 0.2)',
                      color: '#dc3545',
                      fontSize: '0.9rem',
                      padding: '10px 20px',
                      transition: 'all 0.3s ease',
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px'
                    }}
                  >
                    <FaTimes /> حذف الحساب الأونلاين
                  </button>
                </div>
                {/* كارت الحساب ال��وفلاين */}
                {localUserData ? (
                  <div style={{
                    flex: '1',
                    border: activeAccount === 'local' ? '2px solid #388e3c' : '1px solid #e0e7ef',
                    borderRadius: '20px',
                    background: activeAccount === 'local' ? 'linear-gradient(145deg, #f0fff2 0%, #ffffff 100%)' : 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                    boxShadow: activeAccount === 'local' ? '0 8px 24px rgba(56, 142, 60, 0.12)' : '0 4px 16px rgba(0, 0, 0, 0.05)',
                    padding: '32px',
                    minWidth: '280px',
                    textAlign: 'center',
                    position: 'relative',
                    cursor: 'pointer',
                    opacity: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    transition: 'all 0.3s ease',
                    transform: activeAccount === 'local' ? 'translateY(-4px)' : 'none',
                  }}
                  onClick={() => {
                    setActiveAccount('local');
                    setActiveAccountState('local');
                  }}
                  >
                    {activeAccount === 'local' && (
                      <div style={{
                        marginBottom: 16,
                        background: 'linear-gradient(135deg, #388e3c 0%, #4caf50 100%)',
                        color: '#fff',
                        borderRadius: '12px',
                        padding: '6px 16px',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        alignSelf: 'center',
                        boxShadow: '0 4px 12px rgba(56, 142, 60, 0.15)',
                        letterSpacing: '0.3px',
                      }}>
                        الحساب الحالي
                      </div>
                    )}
                    <div style={{
                      background: activeAccount === 'local' ? 'rgba(56, 142, 60, 0.1)' : 'rgba(0, 0, 0, 0.03)',
                      borderRadius: '50%',
                      padding: '20px',
                      marginBottom: '16px',
                      transition: 'all 0.3s ease'
                    }}>
                      <FaMobileAlt size={40} color={activeAccount === 'local' ? '#388e3c' : '#666'} />
                    </div>
                    <div style={{
                      fontWeight: '600',
                      fontSize: '1.2rem',
                      marginBottom: '8px',
                      color: activeAccount === 'local' ? '#388e3c' : '#333'
                    }}>الحساب الأوفلاين</div>
                    <div style={{
                      fontSize: '1rem',
                      color: '#444',
                      marginBottom: '12px',
                      maxWidth: '90%',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>{localUserData?.email || ''}</div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: '#666',
                      marginBottom: '16px',
                      background: 'rgba(0, 0, 0, 0.03)',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      width: '90%'
                    }}>بياناتك محفوظة محلياً</div>
                    <div style={{flex: 1}}></div>
                    <button
                      onClick={e => {
                        e.stopPropagation();
                        handleDeleteAccountClick('local');
                      }}
                      className={styles.deleteAccountButton}
                      style={{
                        marginTop: 18,
                        width: '90%',
                        background: 'rgba(220, 53, 69, 0.05)',
                        backdropFilter: 'blur(8px)',
                        border: '1px solid rgba(220, 53, 69, 0.2)',
                        color: '#dc3545',
                        fontSize: '0.9rem',
                        padding: '10px 20px',
                        transition: 'all 0.3s ease',
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px'
                      }}
                    >
                      <FaTimes /> حذف الحساب الأوفلاين
                    </button>
                  </div>
                ) : (
                  <div style={{
                    flex: '1',
                    border: '2px dashed rgba(56, 142, 60, 0.3)',
                    borderRadius: '20px',
                    background: 'linear-gradient(145deg, #f8fdf6 0%, #ffffff 100%)',
                    color: '#388e3c',
                    padding: '32px',
                    minWidth: '280px',
                    textAlign: 'center',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    overflow: 'hidden',
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.05)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '16px'
                  }}
                  onClick={() => setShowConfirmModal(true)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(56, 142, 60, 0.15)';
                    e.currentTarget.style.background = 'linear-gradient(145deg, #e8f5e8, #c8e6c9)';
                    e.currentTarget.style.borderColor = '#81c784';
                    const icon = e.currentTarget.querySelector('svg');
                    if (icon) {
                      icon.style.transform = 'scale(1.1)';
                      icon.style.color = '#2e7d32';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                    e.currentTarget.style.background = 'linear-gradient(145deg, #f1f8e9, #e8f5e8)';
                    e.currentTarget.style.borderColor = '#c8e6c9';
                    const icon = e.currentTarget.querySelector('svg');
                    if (icon) {
                      icon.style.transform = 'scale(1)';
                      icon.style.color = '#388e3c';
                    }
                  }}
                  >
                    <div style={{
                      background: 'rgba(56, 142, 60, 0.1)',
                      borderRadius: '50%',
                      padding: '24px',
                      marginBottom: '8px',
                      transition: 'all 0.3s ease'
                    }}>
                      <FaMobileAlt size={40} color="#388e3c" />
                    </div>
                    <div style={{
                      fontWeight: '600',
                      fontSize: '1.2rem',
                      marginBottom: '8px',
                      color: '#388e3c'
                    }}>إضافة حساب أوفلاين</div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: '#666',
                      marginBottom: '16px',
                      background: 'rgba(0, 0, 0, 0.03)',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      width: '90%'
                    }}>يمكنك إنشاء حساب محلي منفصل</div>
                  </div>
                )}
              </div>

              {/* خط خفيف أسفل كروت الحسابات */}
              <div className={styles.accountCardsDivider}></div>

                {/* جملة توضيحية وخانة إدارة المجموعات - آخر خانة */}
                <div style={{fontSize: '0.98rem', color: '#555', margin: '18px 0 8px 0', textAlign: 'right'}}>
                  يمكنك إدارة أعضاء المجموعات وتحديد صلاحياتهم لضمان توزيع المهام والتحكم في مستوى الوصول داخل النظام.
                </div>
                <div className={styles.profileField} onClick={() => navigate('/groups')} style={{cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                  <label style={{display: 'flex', alignItems: 'center', margin: 0}}><FaUsers className={styles.fieldIcon} /> إدارة المجموعات:</label>
                  <FaChevronLeft style={{marginLeft: '10px', fontSize: '0.9rem', opacity: 0.7}} />
                </div>
              </div>
            </div>
          )}

          {/* Payments and Subscriptions Section */}
          {activeSection === 'payments' && (
            <div className={styles.personalInfoSection}> 
              <h3 className={styles.sectionTitle}>معلومات الدفع والاشتراكات</h3>
              <div className={styles.profileDetails}>
                <div className={styles.profileField}>
                  <label><FaBuilding className={styles.fieldIcon} /> طريقة الدفع:</label>
                  <span>لا توجد طرق دفع مسجلة</span>
                </div>
                <div className={styles.profileField}>
                  <label><FaBuilding className={styles.fieldIcon} /> الاشتراك الحالي:</label>
                  <span>الخطة المجانية</span>
                </div>
              </div>
            </div>
          )}

          {/* Security Section */}
          {activeSection === 'security' && (
            <div className={styles.personalInfoSection}>
              <h3 className={styles.sectionTitle}>الأمان</h3>
              <p style={{
                color: '#5f6368',
                fontSize: '14px',
                marginBottom: '20px',
                fontStyle: 'italic'
              }}>اقتراحات وإعدادات لمساعدتكَ في الحفاظ على أمان حسابكِ</p>
              <div className={styles.profileDetails}>
                <div
                  className={styles.profileField}
                  onClick={() => navigate('/view-email')}
                >
                  <label><FaEnvelope className={styles.fieldIcon} /> البريد الإلكتروني:</label>
                  <span style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>{currentDisplayData?.email || currentUser?.email || 'غير محدد'} <FaChevronLeft style={{marginLeft: '10px', fontSize: '0.9rem', opacity: 0.7}} /></span>
                </div>
                <div
                  className={styles.profileField}
                  onClick={() => navigate('/edit-password')}
                >
                  <label><FaUserTie className={styles.fieldIcon} /> كلمة المرور:</label>
                  <span style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>•••••••• <FaChevronLeft style={{marginLeft: '10px', fontSize: '0.9rem', opacity: 0.7}} /></span>
                </div>
                <div
                  className={styles.profileField}
                  onClick={() => navigate('/edit-field/phone')}
                >
                  <label><FaPhone className={styles.fieldIcon} /> رقم الهاتف:</label>
                  <span style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>{currentDisplayData?.phone || 'غير محدد'} <FaChevronLeft style={{marginLeft: '10px', fontSize: '0.9rem', opacity: 0.7}} /></span>
                </div>
              </div>
            </div>
          )}

          {/* Themes Section */}
          {activeSection === 'themes' && (
            <div className={styles.personalInfoSection}>
              <h3 className={styles.sectionTitle}>المظهر والثيمات</h3>
              <p style={{
                color: '#5f6368',
                fontSize: '14px',
                marginBottom: '20px',
                fontStyle: 'italic'
              }}>اختر المظهر المفضل لديك من بين الثيمات المتاحة</p>
              <ThemeSelector />
            </div>
          )}
          
          {/* Edit Field Section */}
          {activeSection === 'editField' && (
            <div className={styles.personalInfoSection}>
              <EditFieldForm
                editingField={editingField}
                formData={formData}
                handleChange={handleChange}
                handleSave={handleSave}
                handleCancel={() => {
                  setActiveSection('personal');
                  setEditingField(null);
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Modal for delete account confirmation */}
      {showDeleteModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal} style={{maxWidth: '450px'}}>
            <h3 style={{color: '#d32f2f', marginBottom: '20px'}}>
              تأكيد حذف الحساب {deleteAccountType === 'online' ? 'الأونلاين' : 'المحلي'}
            </h3>
            

            
            <p style={{marginBottom: '20px', color: '#666'}}>
              هذا الإجراء لا يمكن التراجع عنه. لتأكيد الحذف، يرجى إدخال كلمة المرور الخاصة بك:
            </p>
            
            <div style={{marginBottom: '20px'}}>
              <input
                type="password"
                placeholder="كلمة المرور"
                value={passwordConfirm}
                onChange={(e) => setPasswordConfirm(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleDeleteConfirm();
                  }
                }}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '16px',
                  direction: 'ltr'
                }}
                autoFocus
              />
            </div>
            
            <div className={styles.modalButtons}>
              <button 
                onClick={handleDeleteConfirm} 
                className={styles.deleteButton}
                style={{
                  backgroundColor: '#d32f2f',
                  color: 'white',
                  padding: '12px 20px',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  fontWeight: 'bold'
                }}
              >
                تأكيد الحذف
              </button>
              <button 
                onClick={cancelDeleteAccount} 
                className={styles.cancelButton}
                style={{
                  backgroundColor: '#f5f5f5',
                  color: '#333',
                  padding: '12px 20px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px',
                  cursor: 'pointer'
                }}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal for creating local account */} 
      {showConfirmModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal} style={{maxWidth: '500px'}}>
            {/* Header with icon */}
            <div style={{textAlign: 'center', marginBottom: '20px'}}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: '#e8f5e8',
                marginBottom: '15px'
              }}>
                <FaMobileAlt size={28} color="#388e3c" />
              </div>
              <h3 style={{color: '#388e3c', margin: '0', fontSize: '1.4rem', fontWeight: '600'}}>
                إنشاء حساب أوفلاين جديد
              </h3>
            </div>

            {/* Information box */}
            <div style={{
              backgroundColor: '#e8f5e8',
              border: '1px solid #c8e6c9',
              borderRadius: '12px',
              padding: '20px',
              marginBottom: '20px',
              textAlign: 'right'
            }}>
              <div style={{fontWeight: 'bold', color: '#2e7d32', marginBottom: '10px', fontSize: '1.1rem'}}>
                🏠 ما هو الحساب الأوفلاين؟
              </div>
              <ul style={{
                margin: '0',
                paddingRight: '20px',
                color: '#2e7d32',
                lineHeight: '1.6'
              }}>
                <li>يعمل بدون اتصال بالإنترنت</li>
                <li>يحفظ البيانات على جهازك فقط</li>
                <li>منفصل تماماً عن الحساب الأونلاين</li>
                <li>مناسب للاستخدام في المناطق ضعيفة الإنترنت</li>
              </ul>
            </div>

            {/* Warning box */}
            <div style={{
              backgroundColor: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '8px',
              padding: '15px',
              marginBottom: '25px',
              textAlign: 'right'
            }}>
              <div style={{fontWeight: 'bold', color: '#856404', marginBottom: '8px'}}>
                ⚠️ ملاحظة مهمة:
              </div>
              <p style={{margin: '0', color: '#856404', fontSize: '0.95rem'}}>
                سيتم إنشاء حساب فارغ جديد. يمكنك نقل البيانات لاحقاً من الحساب الأونلاين إذا أردت.
              </p>
            </div>

            {/* Question */}
            <p style={{
              textAlign: 'center',
              fontSize: '1.1rem',
              color: '#555',
              margin: '0 0 25px 0',
              fontWeight: '500'
            }}>
              هل تريد المتابعة وإنشاء الحساب الأوفلاين؟
            </p>

            {/* Buttons */}
            <div className={styles.modalButtons}>
              <button 
                onClick={createLocalAccount} 
                className={styles.confirmButton}
                style={{
                  background: '#388e3c',
                  color: 'white',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '15px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <FaMobileAlt size={16} />
                نعم، إنشاء الحساب
              </button>
              <button 
                onClick={cancelCreateLocalAccount} 
                className={styles.cancelButton}
                style={{
                  background: '#f5f5f5',
                  color: '#666',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  border: '1px solid #ddd',
                  fontSize: '15px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePage;