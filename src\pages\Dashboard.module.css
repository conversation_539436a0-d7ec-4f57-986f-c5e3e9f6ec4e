
@import '../styles/variables.css';

/* تخصيص خاص بصفحة لوحة التحكم */
.dashboard {
  font-family: var(--font-family-primary);
  background-color: var(--current-bg-secondary);
  color: var(--current-text-primary);
  line-height: var(--line-height-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
}

.dashboard h1,
.dashboard h2,
.dashboard h3,
.dashboard h4,
.dashboard h5,
.dashboard h6 {
  font-weight: var(--font-weight-medium);
  color: var(--current-text-primary);
}

.content {
  flex: 1;
  padding: 2rem; /* Consistent padding */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  text-align: center;
}

/* Header Section */
.header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 2rem;
}

/* Logo Container */
.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.mainLogo {
  height: 90px;
  width: auto;
  max-width: 100%;
  object-fit: contain;
}

/* Search Container */
.searchContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
}

.searchBox {
  position: relative;
  width: 640px;
  margin: 0 auto;
}

.searchIcon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 16px;
  z-index: 1;
}

.searchInput {
  width: 640px;
  padding: 8px 36px 8px 10px;
  border: 1px solid var(--neutral-200);
  border-radius: 30px;
  font-size: 14px;
  background-color: var(--page-background);
  color: var(--current-text-primary);
  transition: var(--transition-normal);
  height: 52px;
  box-shadow:
    0 4px 15px var(--shadow-light),
    0 10px 30px var(--shadow-light);
  backdrop-filter: var(--current-backdrop-blur);
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow:
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  background-color: var(--page-background);
}

.searchInput::placeholder {
  color: var(--current-text-tertiary);
  font-size: 14px;
}

/* زر قائمة القضايا */
.caseListButtonContainer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.caseListButton {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--current-shadow-light);
  backdrop-filter: var(--current-backdrop-blur);
  max-width: 150px;
}

.caseListButton:hover {
  background: var(--primary-medium-blue);
  transform: translateY(-2px);
  box-shadow: var(--current-shadow-medium);
}

h1 {
  font-size: 2.25rem; /* Larger, more prominent heading */
  font-weight: 400; /* Lighter weight for main title */
  color: var(--google-grey-700);
}

/* Account Status Card */
.accountStatus {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  background-color: var(--current-bg-primary);
  border-radius: var(--radius-md);
  font-size: 1rem;
  color: var(--current-text-primary);
  flex-wrap: wrap;
  justify-content: center;
  box-shadow: var(--current-shadow-medium);
  border: 1px solid var(--current-border-primary);
  backdrop-filter: var(--current-backdrop-blur);
  transition: all var(--transition-normal);
}

.onlineText {
  color: var(--google-blue-500); /* Google Blue for online */
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500; /* Medium weight */
}

.localText {
  color: var(--google-green-500); /* Google Green for local */
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.accountIcon {
  font-size: 1.1em;
}

.accountNote {
  font-size: 0.85rem;
  color: var(--google-grey-500);
  margin-left: 10px;
}



/* Spacing for Assigned Tasks Section */
.assignedTasksSection {
  margin-top: 2rem;
}

/* Loading Spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingSpinner {
  border: 4px solid var(--google-grey-200);
  border-left: 4px solid var(--google-blue-500); /* Google Blue for spinner */
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

/* Arabic Specific Styles (ensure consistency with Material Design) */
.arabicContainer {
  direction: rtl;
  font-family: var(--font-family-primary);
  padding: 24px;
  background-color: var(--google-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-1);
  flex: 1;
  color: var(--google-grey-900);
  border: 1px solid var(--google-grey-200);
}

.arabicHeader {
  display: flex;
  align-items: center;
  margin-bottom: 28px;
}

.arabicBackButton {
  display: flex;
  align-items: center;
  background-color: var(--google-grey-100);
  padding: 10px 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--google-grey-700);
  transition: all var(--transition-normal);
  border: 1px solid var(--google-grey-200);
  font-weight: 500;
}

.arabicBackButton:hover {
  background-color: var(--google-grey-200);
  transform: translateY(-2px);
  color: var(--google-grey-900);
}

.arabicButtonIcon {
  margin-left: 8px;
}

.arabicTitle {
  margin: 0;
  font-size: 24px;
  color: var(--google-grey-900);
  flex-grow: 1;
  font-weight: 600;
}

.arabicReportsSection {
  margin-bottom: 25px;
}

.arabicReportCard {
  background-color: var(--google-white);
  border: 1px solid var(--google-grey-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  border-right: 4px solid var(--google-blue-500); /* Use Google Blue for accent */
  box-shadow: var(--shadow-1);
  transition: all var(--transition-normal);
}

.arabicReportCard:hover {
  box-shadow: var(--shadow-2);
  border-color: var(--google-blue-600); /* Darker blue on hover */
}

.arabicReportDate {
  font-weight: 600;
  color: var(--google-grey-900);
  margin-bottom: 8px;
  font-size: 16px;
}

.arabicReportContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicReportText {
  color: var(--google-grey-700);
  line-height: 1.6;
  font-size: 15px;
}

.arabicHistoryButton {
  background: none;
  border: none;
  color: var(--google-blue-500); /* Google Blue for buttons */
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
  transition: all var(--transition-normal);
  width: auto;
  padding: 6px 12px;
  border-radius: var(--radius-sm); /* Slightly rounded button */
}

.arabicHistoryButton:hover {
  color: var(--google-blue-600);
  background-color: rgba(66, 133, 244, 0.1); /* Light blue hover background */
  border-radius: var(--radius-md);
}

.arabicDivider {
  margin: 28px 0;
  display: flex;
  justify-content: center;
}

.arabicLine {
  width: 100%;
  height: 1px;
  background-color: var(--google-grey-200);
}

.arabicActionsSection {
  margin-top: 20px;
}

.arabicSectionTitle {
  font-size: 20px;
  color: var(--google-grey-900);
  margin-bottom: 15px;
}

.arabicActionCard {
  background-color: var(--google-white);
  border: 1px solid var(--google-grey-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-1);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.arabicActionCard:hover {
  box-shadow: var(--shadow-2);
  border-color: var(--google-blue-500);
}

.arabicActionCard::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: var(--google-blue-500); /* Google Blue accent */
}

.arabicActionTitle {
  font-weight: 600;
  color: var(--google-grey-900);
  margin-bottom: 10px;
  font-size: 16px;
}

.arabicActionDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicActionDeadline {
  color: var(--google-grey-700);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Responsive adjustments */
@media (min-width: 1200px) {
  .content {
    padding: 3rem; /* Even more padding on large screens */
  }
  .grid {
    gap: 2rem;
  }

}

@media (max-width: 991px) {
  .content {
    padding: 1.5rem;
  }

  .searchBox {
    width: 90%;
    max-width: 400px;
  }

  .searchInput {
    width: 100%;
  }

  h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  .accountStatus {
    font-size: 0.9rem;
    padding: 10px 16px;
  }

  .assignedTasksSection {
    margin-top: 2rem;
  }

  /* تحسين الشعار وخانة البحث للهواتف */
  .mainLogo {
    height: 60px;
  }

  .searchBox {
    width: 90%;
    max-width: 400px;
  }

  .searchInput {
    width: 100%;
    padding: 12px 36px 12px 10px;
    font-size: 14px;
    height: 48px;
  }

  .searchIcon {
    right: 12px;
    font-size: 16px;
  }

  .header {
    gap: 20px;
  }

  .caseListButton {
    padding: 8px 16px;
    font-size: 13px;
    max-width: 130px;
  }
}

@media (max-width: 576px) {
  .content {
    padding: 0.75rem;
  }

  h1 {
    font-size: 1.5rem;
  }
  p {
    font-size: 0.9rem;
  }

  .accountNote {
    margin-left: 0;
    margin-top: 5px;
    text-align: center;
    width: 100%;
  }

  .assignedTasksSection {
    margin-top: 1.5rem;
  }

  /* تحسين إضافي للهواتف الصغيرة */
  .mainLogo {
    height: 50px;
  }

  .searchBox {
    width: 95%;
    max-width: 350px;
  }

  .searchInput {
    width: 100%;
    padding: 10px 36px 10px 10px;
    font-size: 14px;
    height: 44px;
  }

  .searchIcon {
    right: 10px;
    font-size: 14px;
  }

  .header {
    gap: 15px;
  }

  .caseListButton {
    padding: 6px 12px;
    font-size: 12px;
    max-width: 120px;
  }
}