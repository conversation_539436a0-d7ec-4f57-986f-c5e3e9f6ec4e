# دليل نظام الصلاحيات

## كيفية عمل النظام

تم تطبيق نظام صلاحيات ديناميكي يربط بين إدارة المجموعات وواجهة المستخدم.

## الميزات المضافة

### 1. خدمة إدارة الصلاحيات
- ملف: `src/services/PermissionsService.js`
- يدير الصلاحيات للأدوار المختلفة (مدير، محرر، عضو)
- يحفظ البيانات في localStorage
- يوفر وظائف للتحقق من الصلاحيات
- **يعتمد على النظام الأصلي:** يستخدم أدوار المستخدمين من قاعدة البيانات

### 2. ربط الصلاحيات بالواجهة
- **TopBar.jsx**: إخفاء/إظهار الأزرار حسب صلاحيات المستخدم:
  - **زر "إضافة قضية"**: يظهر/يختفي حسب صلاحية `addCases`
  - **زر "الإشعارات"**: يظهر/يختفي حسب صلاحية `viewNotifications`

- **CaseFollowUpModal.jsx**: إدارة حذف الملاحظات:
  - **حذف الملاحظات بالضغط المطول**: يعمل فقط مع صلاحية `deleteData`
  - عرض قفل 🔒 على الملاحظات غير القابلة للحذف

- **ReportHistory.jsx**: إدارة المهام والتكليفات:
  - **زر حذف المهام**: يظهر/يختفي حسب صلاحية `deleteData`
  - **أزرار إضافة التنبيهات**: تظهر/تختفي حسب صلاحية `addData`
  - **زر تكليف الأعضاء**: يظهر/يختفي حسب صلاحية `assignTasks`

- **GroupsManagement.jsx**: إدارة المجموعات والأعضاء:
  - **أزرار إضافة/حذف الأعضاء**: تظهر/تختفي حسب صلاحية `manageMembers`
  - **أزرار إنشاء/حذف المجموعات**: تظهر/تختفي حسب صلاحية `manageMembers`
  - **رسالة تنبيه**: تظهر عند عدم وجود صلاحية إدارة الأعضاء

- الصلاحيات تعتمد على دور المستخدم الحقيقي في المجموعات

### 3. واجهة تفاعلية للصلاحيات
- تم تحديث صفحة الصلاحيات في `GroupsManagement.jsx`
- checkbox قابل للنقر لكل صلاحية
- حفظ فوري للتغييرات
- تأثيرات بصرية جميلة

## كيفية الاستخدام

### خطوات الاختبار:

1. **انتقل إلى صفحة إدارة المجموعات**
   - اذهب إلى `/groups`

2. **انقر على تبويب "الصلاحيات"**

3. **اختبر الصلاحيات المختلفة:**
   - **لاختبار زر إضافة القضايا:**
     - ألغِ تعليم "إمكانية إضافة القضايا" لدورك الحالي
     - ستجد أن زر "إضافة قضية" اختفى من الشريط العلوي
   
   - **لاختبار زر الإشعارات:**
     - ألغِ تعليم "إمكانية رؤية الإشعارات" لدورك الحالي  
     - ستجد أن زر "الإشعارات" اختفى من الشريط العلوي

   - **لاختبار حذف البيانات:**
     - ألغِ تعليم "إمكانية حذف البيانات" لدورك الحالي
     - اذهب إلى قضية واختبر:
       - الضغط المطول على ملاحظة → لن يعمل الحذف وتظهر أيقونة القفل 🔒
       - النظر في قائمة المهام → زر الحذف (🗑️) سيختفي

   - **لاختبار إضافة التنبيهات:**
     - ألغِ تعليم "إمكانية إضافة البيانات" لدورك الحالي
     - اذهب إلى قضية وانظر في قائمة المهام
     - ستجد أن أزرار "إضافة تنبيه بتاريخ جلسة" و "إضافة تنبيه بإجراء" اختفت
     - بدلاً منها ستظهر رسالة: "🔒 ليس لديك صلاحية إضافة تنبيهات جديدة"

   - **لاختبار إدارة الأعضاء:**
     - ألغِ تعليم "إمكانية إضافة أعضاء وحذف أعضاء" لدورك الحالي
     - اذهب إلى `/groups` وانظر في تبويبات "المجموعات" و "الأعضاء"
     - ستجد أن أزرار "إضافة عضو" و "إزالة" و "إنشاء مجموعة" اختفت
     - بدلاً منها ستظهر رسائل: "🔒 لا يمكن إضافة أعضاء" أو "🔒 غير مسموح"

   - **لاختبار تكليف الأعضاء:**
     - ألغِ تعليم "إمكانية تكليف الأعضاء" لدورك الحالي
     - اذهب إلى قضية وانظر في قائمة المهام
     - ستجد أن زر التكليف اختفى

4. **استعادة الصلاحيات:**
   - أعد تعليم الصلاحيات لترى الأزرار تظهر مرة أخرى

5. **مراقبة المعلومات:**
   - تحقق من قسم "معلومات المستخدم الحالي" لرؤية حالة الصلاحيات الحالية

## الصلاحيات المتاحة

- **إمكانية حذف البيانات**: `deleteData` (تشمل حذف الملاحظات والمهام)
- **إمكانية إضافة البيانات**: `addData` (ترتبط بأزرار إضافة التنبيهات)
- **إمكانية إضافة القضايا**: `addCases` (ترتبط بزر إضافة القضايا)
- **إمكانية رؤية الإشعارات**: `viewNotifications`
- **إمكانية إضافة أعضاء وحذف أعضاء**: `manageMembers`
- **إمكانية تكليف الأعضاء**: `assignTasks`

## الأدوار الافتراضية

### مدير (Admin)
- جميع الصلاحيات مفعلة

### محرر (Editor)
- إضافة القضايا ✓
- إضافة البيانات ✓
- رؤية الإشعارات ✓
- تكليف الأعضاء ✓
- حذف البيانات ✗
- إدارة الأعضاء ✗

### عضو (Member)
- رؤية الإشعارات فقط ✓
- باقي الصلاحيات ✗

## التطوير المستقبلي

يمكن بسهولة إضافة المزيد من الصلاحيات لتشمل:
- إخفاء/إظهار أقسام معينة
- تقييد الوصول لصفحات معينة
- التحكم في مستوى التفاصيل المعروضة

## ملاحظات تقنية

- البيانات تحفظ في localStorage
- النظام يراقب التغییرات ويحدث الواجهة فورياً
- يمكن ربط النظام لاحقاً بقاعدة بيانات حقيقية