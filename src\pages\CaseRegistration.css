/* Modern Case Registration Styles - Light Mode Only */
@import '../styles/variables.css';

/* 
 * Light Mode Variables
 * تم تصميم هذا النموذج للوضع الفاتح فقط لتجربة مستخدم محسنة ومريحة للعين
 */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --primary-lighter: #eff6ff;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --success-lighter: #ecfdf5;
  --error-color: #ef4444;
  --error-light: #fee2e2;
  --error-lighter: #fef2f2;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --warning-lighter: #fffbeb;
  --info-color: #06b6d4;
  --info-light: #cffafe;
  --info-lighter: #f0fdfa;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-light: #d1d5db;
  --background: #f9fafb;
  --background-alt: #f3f4f6;
  --card-background: #ffffff;
  --card-hover: #f8fafc;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-in-out;
  --transition-slow: all 0.5s ease-in-out;
}

/* Main Layout */
.case-registration-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--background) 0%, var(--background-alt) 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  direction: rtl;
  color: var(--text-primary);
}

.main-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Registration Card */
.registration-card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: var(--transition);
}

.registration-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Header */
.card-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transfer-badge {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.transfer-badge span {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Progress Container */
.progress-container {
  padding: 2rem;
  background: var(--background);
  border-bottom: 1px solid var(--border-color);
}

/* Progress Steps */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--border-color);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  transition: var(--transition);
  border: 2px solid transparent;
}

.step-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-align: center;
  font-weight: 500;
  transition: var(--transition);
}

.progress-step.active .step-number {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
}

.progress-step.active .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

.progress-step.current .step-number {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Progress Bar */
.progress-bar {
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  border-radius: 2px;
  transition: width 0.5s ease-in-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  margin: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.message-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.message-text {
  flex: 1;
}

.error-message {
  background: var(--error-light);
  color: var(--error-color);
  border: 1px solid #fecaca;
}

.success-message {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid #a7f3d0;
}

.loading-message {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid #bfdbfe;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--primary-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form */
.registration-form {
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.form-body {
  flex: 1;
  padding: 2rem;
}

/* Step Content */
.step-content {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

/* Status Cards */
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.status-card {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.status-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.status-card.active {
  border-color: var(--primary-color);
  background: var(--primary-light);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.status-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.status-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.status-card.active .status-title {
  color: var(--primary-color);
}

/* Info Note Styles */
.info-note {
  background: linear-gradient(135deg, var(--info-lighter) 0%, var(--info-light) 100%);
  border: 1px solid var(--info-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  animation: fadeInUp 0.5s ease-out;
}

.note-icon {
  font-size: 1.5rem;
  line-height: 1;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.note-content {
  flex: 1;
}

.note-content p {
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
  line-height: 1.6;
}

.note-content p:last-child {
  margin-bottom: 0;
}

.note-content strong {
  color: var(--info-color);
  font-weight: 600;
}

/* Case Number Container */
.case-number-container {
  background: var(--background);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-top: 1rem;
}

.number-inputs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.number-input,
.year-input {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1.125rem;
  text-align: center;
  font-weight: 600;
  background: var(--card-background);
  transition: var(--transition);
  min-width: 120px;
}

.number-input:focus,
.year-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.separator {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.generated-display {
  text-align: center;
  padding: 1rem;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  color: var(--text-secondary);
}

.generated-display strong {
  color: var(--primary-color);
  font-weight: 600;
}

/* Input Rows and Fields */
.input-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-field.full-width {
  grid-column: 1 / -1;
}

.input-field label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.required {
  color: var(--error-color);
  font-size: 0.75rem;
}

.optional {
  color: var(--text-muted);
  font-size: 0.75rem;
}

.input-field input,
.input-field select,
.input-field textarea {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  background: var(--card-background);
  transition: var(--transition);
  font-family: inherit;
}

.input-field input:focus,
.input-field select:focus,
.input-field textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.input-field input:disabled,
.input-field select:disabled {
  background: var(--background);
  color: var(--text-muted);
  cursor: not-allowed;
}

.input-field textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.helper-note {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
  font-style: italic;
}

/* Form Navigation */
.form-navigation {
  background: var(--background);
  border-top: 1px solid var(--border-color);
  padding: 1.5rem 2rem;
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.prev-btn {
  background: var(--card-background);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.prev-btn:hover {
  background: var(--background);
  border-color: var(--text-secondary);
  color: var(--text-primary);
}

.next-btn {
  background: var(--primary-color);
  color: white;
  border: 2px solid var(--primary-color);
}

.next-btn:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.next-btn.disabled {
  background: var(--border-color);
  color: var(--text-muted);
  border-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Final Buttons */
.final-buttons {
  display: flex;
  gap: 1rem;
  margin-right: auto;
}

.submit-btn {
  background: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-sm);
}

.submit-btn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.submit-btn.disabled {
  background: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-btn {
  background: var(--card-background);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn:hover {
  background: var(--background);
  border-color: var(--text-secondary);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-container {
    padding: 1rem 0.5rem;
  }

  .registration-card {
    margin: 0 0.5rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .progress-container {
    padding: 1.5rem;
  }

  .progress-steps {
    flex-direction: column;
    gap: 1rem;
  }

  .progress-step {
    flex-direction: row;
    text-align: right;
  }

  .step-number {
    margin-bottom: 0;
    margin-left: 1rem;
  }

  .form-body {
    padding: 1.5rem;
  }

  .form-navigation {
    padding: 1rem 1.5rem;
  }

  .status-cards {
    grid-template-columns: 1fr;
  }

  .input-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .number-inputs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .number-input,
  .year-input {
    min-width: 100%;
  }

  .final-buttons {
    flex-direction: column;
    width: 100%;
  }

  .submit-btn,
  .cancel-btn {
    width: 100%;
    justify-content: center;
  }

  .nav-buttons {
    flex-direction: column;
  }

  .nav-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .progress-container {
    padding: 1rem;
  }

  .form-body {
    padding: 1rem;
  }

  .form-navigation {
    padding: 1rem;
  }

  .status-card {
    padding: 1rem;
  }

  .status-icon {
    font-size: 1.5rem;
  }

  .form-section h3 {
    font-size: 1.25rem;
  }

  .input-field input,
  .input-field select,
  .input-field textarea {
    padding: 0.625rem 0.75rem;
  }
}

/* Print Styles */
@media print {
  .case-registration-page {
    background: white;
  }

  .registration-card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }

  .card-header {
    background: white !important;
    color: var(--text-primary) !important;
  }

  .progress-container,
  .form-navigation {
    display: none;
  }
}

/* Enhanced Light Mode Features */
.card-background-subtle {
  background: var(--card-hover);
}

.border-subtle {
  border-color: var(--border-light);
}

.text-subtle {
  color: var(--text-light);
}

/* Light Mode Enhancements */
.light-emphasis {
  background: linear-gradient(135deg, var(--primary-lighter) 0%, var(--primary-light) 100%);
  border: 1px solid var(--primary-light);
}

.light-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.light-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.light-input {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
}

.light-input:focus {
  border-color: var(--primary-color);
  background: var(--card-background);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.light-button {
  background: var(--primary-color);
  color: white;
  border: none;
  box-shadow: var(--shadow-sm);
}

.light-button:hover {
  background: var(--primary-hover);
  box-shadow: var(--shadow-md);
}

.light-button-secondary {
  background: var(--card-background);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.light-button-secondary:hover {
  background: var(--background);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Indicators */
.nav-btn:focus,
.submit-btn:focus,
.cancel-btn:focus,
.status-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Loading States */
.submit-btn.loading {
  position: relative;
  color: transparent;
}

.submit-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1rem;
  height: 1rem;
  margin: -0.5rem 0 0 -0.5rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Custom Scrollbar */
.form-body::-webkit-scrollbar {
  width: 6px;
}

.form-body::-webkit-scrollbar-track {
  background: var(--background);
}

.form-body::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.form-body::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Floating Labels Animation */
.input-field.floating-label {
  position: relative;
}

.input-field.floating-label label {
  position: absolute;
  top: 0.75rem;
  right: 1rem;
  background: var(--card-background);
  padding: 0 0.25rem;
  color: var(--text-muted);
  transition: var(--transition);
  pointer-events: none;
}

.input-field.floating-label input:focus + label,
.input-field.floating-label input:not(:placeholder-shown) + label {
  top: -0.5rem;
  font-size: 0.75rem;
  color: var(--primary-color);
}

/* Tooltip Styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--text-primary);
  color: var(--card-background);
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.875rem;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Card Hover Effects */
.registration-card {
  position: relative;
  overflow: hidden;
}

.registration-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.registration-card:hover::before {
  left: 100%;
}

/* Step Indicators */
.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.step-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--border-color);
  margin: 0 4px;
  transition: var(--transition);
}

.step-dot.active {
  background: var(--primary-color);
}

/* Validation Styles */
.input-field.error input,
.input-field.error select,
.input-field.error textarea {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-field.success input,
.input-field.success select,
.input-field.success textarea {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.field-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.field-success {
  color: var(--success-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Enhanced Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.step-content.next {
  animation: slideInRight 0.3s ease-out;
}

.step-content.prev {
  animation: slideInLeft 0.3s ease-out;
}

/* Mobile Improvements */
@media (max-width: 768px) {
  .registration-card::before {
    display: none;
  }
  
  .status-card {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .progress-step {
    min-height: 60px;
  }
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .registration-card {
    border: 0.5px solid var(--border-color);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .step-content {
    animation: none;
  }
  
  .progress-step.current .step-number {
    animation: none;
  }
  
  .progress-fill::after {
    animation: none;
  }
}

/* Enhanced Form Field Styles */
.field-error svg,
.field-success svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.character-count {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-align: left;
  margin-top: 0.25rem;
}

.character-count.warning {
  color: var(--warning-color);
}

.character-count.error {
  color: var(--error-color);
}

/* Progress Info Styles */
.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.current-step {
  font-weight: 500;
}

.progress-percentage {
  font-weight: 600;
  color: var(--primary-color);
}

/* Enhanced Step Styles */
.progress-step.completed .step-number {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.progress-step.completed .step-label {
  color: var(--success-color);
}

.progress-step.error .step-number {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.progress-step.error .step-label {
  color: var(--error-color);
}

.progress-step.pending .step-number {
  background: var(--background);
  color: var(--text-muted);
  border-color: var(--border-color);
}

.progress-step.pending .step-label {
  color: var(--text-muted);
}

/* Clickable Steps */
.progress-step[style*="pointer"] {
  transition: var(--transition);
}

.progress-step[style*="pointer"]:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Form Field Enhancements */
.input-field.floating-label {
  margin-top: 1rem;
}

.input-field.floating-label input {
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
}

.input-field.floating-label label {
  transition: all 0.2s ease-in-out;
  transform-origin: right top;
}

.input-field.floating-label input:focus + label,
.input-field.floating-label input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.85);
  background: var(--card-background);
  padding: 0 0.5rem;
  border-radius: 4px;
}

/* Tooltip Enhancements */
.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--text-primary) transparent transparent transparent;
}

/* Enhanced Hover Effects */
.status-card {
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
  transition: left 0.6s;
}

.status-card:hover::before {
  left: 100%;
}

/* Loading Overlay */
.form-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

/* Light Mode Loading Overlay */
.form-loading-overlay {
  background: rgba(255, 255, 255, 0.95);
}

/* Enhanced Button States */
.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Validation Summary */
.validation-summary {
  background: var(--error-light);
  border: 1px solid var(--error-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
}

.validation-summary h4 {
  color: var(--error-color);
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.validation-summary ul {
  margin: 0;
  padding-right: 1.5rem;
  list-style: none;
}

.validation-summary li {
  color: var(--error-color);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.validation-summary li::before {
  content: '•';
  margin-left: 0.5rem;
}

/* Success States */
.form-success-state {
  text-align: center;
  padding: 3rem 2rem;
}

.form-success-state .success-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  color: var(--success-color);
}

.form-success-state h3 {
  color: var(--success-color);
  margin-bottom: 1rem;
}

.form-success-state p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Keyboard Navigation */
.input-field input:focus,
.input-field select:focus,
.input-field textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.nav-btn:focus-visible,
.submit-btn:focus-visible,
.cancel-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-muted: #000000;
  }
  
  .status-card {
    border-width: 3px;
  }
  
  .input-field input,
  .input-field select,
  .input-field textarea {
    border-width: 3px;
  }
}

/* Reduced Data Mode */
@media (prefers-reduced-data: reduce) {
  .progress-fill::after {
    display: none;
  }
  
  .registration-card::before {
    display: none;
  }
  
  .status-card::before {
    display: none;
  }
}

/* Light Mode Specific Enhancements */
.light-shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.light-border-soft {
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.light-background-paper {
  background: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
}

.light-text-hierarchy {
  color: var(--text-primary);
}

.light-text-hierarchy.secondary {
  color: var(--text-secondary);
}

.light-text-hierarchy.muted {
  color: var(--text-muted);
}

/* Optimized for Light Reading */
.reading-optimized {
  line-height: 1.6;
  letter-spacing: 0.01em;
  color: var(--text-primary);
  background: var(--card-background);
}

.reading-optimized h1,
.reading-optimized h2,
.reading-optimized h3 {
  color: var(--text-primary);
  font-weight: 600;
}

.reading-optimized p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Light Mode Focus States */
.light-focus:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--primary-light);
}

.light-focus:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--primary-light);
}

/* Enhanced Light Mode Accessibility */
@media (prefers-contrast: more) {
  :root {
    --text-primary: #000000;
    --text-secondary: #374151;
    --border-color: #6b7280;
    --primary-color: #1e40af;
  }
}