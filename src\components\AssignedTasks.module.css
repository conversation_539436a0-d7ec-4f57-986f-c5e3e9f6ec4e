.assignedTasksContainer {
  background: var(--page-background, #ffffff);
  border-radius: 20px;
  padding: 0;
  box-shadow: 
    0 20px 60px rgba(42, 46, 112, 0.08),
    0 10px 30px rgba(76, 104, 192, 0.05);
  margin-bottom: 24px;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.assignedTasksContainer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2a2e70, #4c68c0);
}

.assignedTasksContainer:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 70px rgba(42, 46, 112, 0.15),
    0 15px 40px rgba(76, 104, 192, 0.1);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: none;
  flex-shrink: 0;
  background: linear-gradient(135deg, #2a2e70, #4c68c0);
  color: white;
  box-shadow: 0 4px 15px rgba(42, 46, 112, 0.4);
  margin: 0;
}

.header h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.headerIcon {
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  width: 40px;
  height: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.taskCount {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.tasksList {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 20px;
}

.taskCard {
  border: none;
  border-radius: 0;
  padding: 18px 0;
  background: transparent;
  transition: all 0.3s ease;
  border-bottom: 1px dashed rgba(42, 46, 112, 0.1);
  position: relative;
}

.taskCard:last-child {
  border-bottom: none;
}

.taskCard:hover {
  background: rgba(42, 46, 112, 0.02);
  transform: translateX(4px);
}

.taskHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
}

.taskIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(42, 46, 112, 0.1);
  border-radius: 12px;
  color: #2a2e70;
  font-size: 1rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.taskCard:hover .taskIcon {
  background: rgba(42, 46, 112, 0.15);
  transform: scale(1.05);
}

.taskInfo {
  flex: 1;
  min-width: 0;
}

.taskTitle {
  margin: 0 0 6px 0;
  color: #2a2e70;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
  word-wrap: break-word;
}

.taskMeta {
  margin: 0;
  color: rgba(42, 46, 112, 0.6);
  font-size: 0.9rem;
  font-weight: 500;
}

.taskBadge {
  flex-shrink: 0;
}

.typeBadge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  border: none;
  backdrop-filter: blur(10px);
}

.deferralBadge {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.actionBadge {
  background: linear-gradient(135deg, #4caf50, #388e3c);
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.taskDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed rgba(42, 46, 112, 0.1);
}

.taskDateAndBadge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.taskDate {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2a2e70;
  font-size: 0.95rem;
  font-weight: 600;
}

.dateIcon {
  color: rgba(42, 46, 112, 0.7);
  font-size: 0.9rem;
}

.assignmentDate {
  color: #6c757d;
  font-size: 0.75rem;
  font-style: italic;
}

.taskDescription {
  margin-top: 6px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 2px solid #3498db;
}

.taskDescription p {
  margin: 0;
  color: #495057;
  font-size: 0.8rem;
  line-height: 1.3;
}

.noTasks {
  text-align: center;
  padding: 40px 20px;
  color: rgba(42, 46, 112, 0.6);
}

.noTasksIcon {
  font-size: 3rem;
  color: rgba(42, 46, 112, 0.2);
  margin-bottom: 16px;
}

.noTasks p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.offlineMessage {
  text-align: center;
  padding: 40px 20px;
  color: rgba(42, 46, 112, 0.6);
  background: rgba(42, 46, 112, 0.02);
  border-radius: 0;
  border: none;
  margin: 20px;
  border-radius: 16px;
}

.offlineIcon {
  font-size: 2.5rem;
  color: rgba(42, 46, 112, 0.2);
  margin-bottom: 16px;
}

.offlineMessage p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.loadingContainer {
  text-align: center;
  padding: 40px 20px;
  color: rgba(42, 46, 112, 0.6);
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(42, 46, 112, 0.1);
  border-top: 3px solid #2a2e70;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .assignedTasksContainer {
    margin-bottom: 20px;
    border-radius: 16px;
  }
  
  .header {
    padding: 16px;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
  
  .header h3 {
    font-size: 1.1rem;
  }
  
  .headerIcon {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }
  
  .tasksList {
    padding: 16px;
  }
  
  .taskCard {
    padding: 14px 0;
  }
  
  .taskHeader {
    gap: 10px;
  }
  
  .taskIcon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
  
  .taskTitle {
    font-size: 1rem;
  }
  
  .taskMeta {
    font-size: 0.85rem;
  }
  
  .typeBadge {
    padding: 4px 10px;
    font-size: 0.75rem;
    min-width: 50px;
  }
  
  .taskDetails {
    gap: 6px;
    margin-top: 10px;
    padding-top: 10px;
  }
  
  .taskDateAndBadge {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }
  
  .taskDate {
    font-size: 0.9rem;
    flex: 1;
    min-width: 0;
  }
  
  .noTasks, .offlineMessage, .loadingContainer {
    padding: 30px 16px;
  }
  
  .noTasksIcon, .offlineIcon {
    font-size: 2.5rem;
  }
  
  .loadingSpinner {
    width: 28px;
    height: 28px;
  }
}